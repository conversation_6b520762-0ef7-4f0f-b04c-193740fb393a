import { Region, getCurrentRegion, RegionalMockProvider, type RegionalOverride } from './regionMockProvider';

// Test implementation of the abstract class
class TestMockProvider extends RegionalMockProvider<TestConfig> {
  // eslint-disable-next-line @typescript-eslint/no-useless-constructor
  constructor(defaultConfig: TestConfig, regionOverrides: RegionalOverride<TestConfig>) {
    super(defaultConfig, regionOverrides);
  }
}

interface TestConfig {
  enabled: boolean;
  value: number;
  name: string;
  nested: {
    property: string;
    count: number;
  };
  items: string[];
}

describe('regionMockProvider', () => {
  // Store original environment variable
  const originalAwsRegion = process.env.AWS_REGION;

  afterEach(() => {
    // Restore original environment variable
    if (originalAwsRegion !== undefined) {
      process.env.AWS_REGION = originalAwsRegion;
    } else {
      delete process.env.AWS_REGION;
    }
  });

  describe('Region enum', () => {
    it('should have correct region values', () => {
      expect(Region.AUSTRALIA).toBe('ap-southeast-2');
      expect(Region.SINGAPORE).toBe('ap-southeast-1');
      expect(Region.UK).toBe('eu-west-2');
      expect(Region.GERMANY).toBe('eu-central-1');
      expect(Region.US).toBe('us-east-1');
    });

    it('should have all expected regions', () => {
      const regions = Object.values(Region);
      expect(regions).toHaveLength(5);
      expect(regions).toContain('ap-southeast-2');
      expect(regions).toContain('ap-southeast-1');
      expect(regions).toContain('eu-west-2');
      expect(regions).toContain('eu-central-1');
      expect(regions).toContain('us-east-1');
    });
  });

  describe('getCurrentRegion', () => {
    it('should return AWS_REGION from environment when set', () => {
      process.env.AWS_REGION = 'eu-west-2';
      expect(getCurrentRegion()).toBe(Region.UK);
    });

    it('should return AUSTRALIA as default when AWS_REGION is not set', () => {
      delete process.env.AWS_REGION;
      expect(getCurrentRegion()).toBe(Region.AUSTRALIA);
    });

    it('should return AUSTRALIA as default when AWS_REGION is empty', () => {
      process.env.AWS_REGION = '';
      expect(getCurrentRegion()).toBe(Region.AUSTRALIA);
    });

    it('should handle all valid region values', () => {
      process.env.AWS_REGION = 'ap-southeast-1';
      expect(getCurrentRegion()).toBe(Region.SINGAPORE);

      process.env.AWS_REGION = 'eu-central-1';
      expect(getCurrentRegion()).toBe(Region.GERMANY);

      process.env.AWS_REGION = 'us-east-1';
      expect(getCurrentRegion()).toBe(Region.US);
    });
  });

  describe('RegionalMockProvider', () => {
    const defaultConfig: TestConfig = {
      enabled: true,
      value: 100,
      name: 'default',
      nested: {
        property: 'default-property',
        count: 5,
      },
      items: ['item1', 'item2'],
    };

    const regionOverrides: RegionalOverride<TestConfig> = {
      [Region.UK]: {
        enabled: false,
        value: 200,
        nested: {
          property: 'uk-property',
        },
      },
      [Region.US]: {
        name: 'us-config',
        items: ['us-item1', 'us-item2', 'us-item3'],
      },
    };

    let provider: TestMockProvider;

    beforeEach(() => {
      provider = new TestMockProvider(defaultConfig, regionOverrides);
    });

    describe('constructor', () => {
      it('should create provider with default config', () => {
        const simpleProvider = new TestMockProvider(defaultConfig, {});
        expect(simpleProvider).toBeDefined();
      });

      it('should pre-compute configs for all regions', () => {
        // This is tested indirectly through getConfig calls
        expect(provider).toBeDefined();
      });
    });

    describe('getConfig', () => {
      it('should return default config for regions without overrides', () => {
        const config = provider.getConfig(Region.AUSTRALIA);
        expect(config).toEqual(defaultConfig);
      });

      it('should return merged config for UK region', () => {
        const config = provider.getConfig(Region.UK);
        expect(config.enabled).toBe(false); // overridden
        expect(config.value).toBe(200); // overridden
        expect(config.name).toBe('default'); // from default
        expect(config.nested.property).toBe('uk-property'); // overridden
        expect(config.nested.count).toBe(5); // from default (deep merge)
        expect(config.items).toEqual(['item1', 'item2']); // from default
      });

      it('should return merged config for US region', () => {
        const config = provider.getConfig(Region.US);
        expect(config.enabled).toBe(true); // from default
        expect(config.value).toBe(100); // from default
        expect(config.name).toBe('us-config'); // overridden
        expect(config.nested).toEqual(defaultConfig.nested); // from default
        expect(config.items).toEqual(['us-item1', 'us-item2', 'us-item3']); // overridden (array replacement)
      });

      it('should use current region when no region specified', () => {
        process.env.AWS_REGION = 'eu-west-2';
        const config = provider.getConfig();
        expect(config.enabled).toBe(false); // UK override
        expect(config.value).toBe(200); // UK override
      });

      it('should return cloned objects (immutability)', () => {
        const config1 = provider.getConfig(Region.AUSTRALIA);
        const config2 = provider.getConfig(Region.AUSTRALIA);

        expect(config1).not.toBe(config2); // Different object references
        expect(config1).toEqual(config2); // Same content
      });

      it('should not affect original when modifying returned config', () => {
        const config = provider.getConfig(Region.AUSTRALIA);
        config.enabled = false;
        config.nested.property = 'modified';
        config.items.push('new-item');

        const newConfig = provider.getConfig(Region.AUSTRALIA);
        expect(newConfig.enabled).toBe(true);
        expect(newConfig.nested.property).toBe('default-property');
        expect(newConfig.items).toEqual(['item1', 'item2']);
      });

      it('should handle deep nested overrides correctly', () => {
        const deepOverrides: RegionalOverride<TestConfig> = {
          [Region.SINGAPORE]: {
            nested: {
              property: 'singapore-property',
              // count should remain from default
            },
          },
        };

        const deepProvider = new TestMockProvider(defaultConfig, deepOverrides);
        const config = deepProvider.getConfig(Region.SINGAPORE);

        expect(config.nested.property).toBe('singapore-property');
        expect(config.nested.count).toBe(5); // from default
      });

      it('should handle array replacement correctly', () => {
        const config = provider.getConfig(Region.US);
        expect(config.items).toEqual(['us-item1', 'us-item2', 'us-item3']);
        expect(config.items).not.toEqual(defaultConfig.items);
      });

      it('should return default config for regions with no overrides', () => {
        const singaporeConfig = provider.getConfig(Region.SINGAPORE);
        const germanyConfig = provider.getConfig(Region.GERMANY);

        expect(singaporeConfig).toEqual(defaultConfig);
        expect(germanyConfig).toEqual(defaultConfig);
      });
    });

    describe('edge cases', () => {
      it('should handle empty overrides object', () => {
        const emptyProvider = new TestMockProvider(defaultConfig, {});
        const config = emptyProvider.getConfig(Region.UK);
        expect(config).toEqual(defaultConfig);
      });

      it('should handle partial overrides', () => {
        const partialOverrides: RegionalOverride<TestConfig> = {
          [Region.UK]: {
            enabled: false,
            // Only overriding one property
          },
        };

        const partialProvider = new TestMockProvider(defaultConfig, partialOverrides);
        const config = partialProvider.getConfig(Region.UK);

        expect(config.enabled).toBe(false);
        expect(config.value).toBe(100); // from default
        expect(config.name).toBe('default'); // from default
      });

      it('should handle undefined override values', () => {
        const overridesWithUndefined: RegionalOverride<TestConfig> = {
          [Region.UK]: undefined,
        };

        const undefinedProvider = new TestMockProvider(defaultConfig, overridesWithUndefined);
        const config = undefinedProvider.getConfig(Region.UK);
        expect(config).toEqual(defaultConfig);
      });
    });
  });

  describe('type definitions', () => {
    it('should properly type RegionalOverride', () => {
      const override: RegionalOverride<TestConfig> = {
        [Region.UK]: {
          enabled: false,
          nested: {
            property: 'test',
          },
        },
      };

      expect(override[Region.UK]?.enabled).toBe(false);
      expect(override[Region.UK]?.nested?.property).toBe('test');
    });

    it('should allow partial overrides in RegionalOverride', () => {
      const partialOverride: RegionalOverride<TestConfig> = {
        [Region.US]: {
          // Only some properties
          name: 'partial',
        },
      };

      expect(partialOverride[Region.US]?.name).toBe('partial');
    });
  });
});
