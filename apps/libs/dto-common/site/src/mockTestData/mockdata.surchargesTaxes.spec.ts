import { Region } from '@npco/component-dto-test-utils/dist/regionMockProvider';

import type { SurchargesTaxesSettings } from '../types';

import { surchargesTaxes, getSurchargesTaxesForRegion } from './mockdata.surchargesTaxes';
import { taxes } from './mockdata.taxes';

describe('mockdata.surchargesTaxes', () => {
  describe('surchargesTaxes export', () => {
    it('should be defined', () => {
      expect(surchargesTaxes).toBeDefined();
    });

    it('should have all required properties', () => {
      expect(surchargesTaxes).toHaveProperty('surchargeAllowed');
      expect(surchargesTaxes).toHaveProperty('surchargeEnabled');
      expect(surchargesTaxes).toHaveProperty('surchargePercent');
      expect(surchargesTaxes).toHaveProperty('feePercent');
      expect(surchargesTaxes).toHaveProperty('gstEnabled');
      expect(surchargesTaxes).toHaveProperty('gstPercent');
      expect(surchargesTaxes).toHaveProperty('taxes');
      expect(surchargesTaxes).toHaveProperty('surchargeFullFees');
      expect(surchargesTaxes).toHaveProperty('surchargeEnabledMoto');
      expect(surchargesTaxes).toHaveProperty('surchargeFullFeesMoto');
      expect(surchargesTaxes).toHaveProperty('surchargePercentMoto');
      expect(surchargesTaxes).toHaveProperty('feePercentMoto');
    });

    it('should have all fee surcharge properties', () => {
      expect(surchargesTaxes).toHaveProperty('feesSurchargeCp');
      expect(surchargesTaxes).toHaveProperty('feesSurchargeMoto');
      expect(surchargesTaxes).toHaveProperty('feesSurchargeXinv');
      expect(surchargesTaxes).toHaveProperty('feesSurchargeZinv');
      expect(surchargesTaxes).toHaveProperty('feesSurchargeCpoc');
      expect(surchargesTaxes).toHaveProperty('feesSurchargeVt');
      expect(surchargesTaxes).toHaveProperty('feesSurchargePbl');
    });

    it('should have correct default values', () => {
      expect(surchargesTaxes.surchargeAllowed).toBe(true);
      expect(surchargesTaxes.surchargeEnabled).toBe(false);
      expect(surchargesTaxes.surchargePercent).toBe(1);
      expect(surchargesTaxes.feePercent).toBe(1);
      expect(surchargesTaxes.gstEnabled).toBe(false);
      expect(surchargesTaxes.gstPercent).toBe(11);
      expect(surchargesTaxes.taxes).toEqual(taxes);
      expect(surchargesTaxes.surchargeFullFees).toBe(false);
      expect(surchargesTaxes.surchargeEnabledMoto).toBe(false);
      expect(surchargesTaxes.surchargeFullFeesMoto).toBe(false);
      expect(surchargesTaxes.surchargePercentMoto).toBe(13);
      expect(surchargesTaxes.feePercentMoto).toBe(12);
    });
  });

  describe('feesSurchargeCp', () => {
    it('should have correct structure and values', () => {
      const { feesSurchargeCp } = surchargesTaxes;
      expect(feesSurchargeCp).toEqual({
        surchargeEnabled: true,
        feeFixed: 10,
        surchargePercent: 1,
        feePercent: 1,
        surchargeFullFees: true,
      });
    });
  });

  describe('feesSurchargeMoto', () => {
    it('should have correct structure and values', () => {
      const { feesSurchargeMoto } = surchargesTaxes;
      expect(feesSurchargeMoto).toEqual({
        surchargeEnabled: true,
        surchargePercent: 13,
        feeFixed: 10,
        feePercent: 12,
        surchargeFullFees: true,
      });
    });
  });

  describe('feesSurchargeXinv (international rates)', () => {
    it('should have correct structure with international properties', () => {
      const { feesSurchargeXinv } = surchargesTaxes;
      expect(feesSurchargeXinv).toEqual({
        surchargeEnabled: true,
        surchargePercent: 18,
        feePercent: 19,
        feeFixed: 20,
        surchargeFullFees: true,
        surchargePercentIntl: 21,
        feePercentIntl: 22,
        feeFixedIntl: 23,
      });
    });
  });

  describe('feesSurchargeZinv (international rates)', () => {
    it('should have correct structure with international properties', () => {
      const { feesSurchargeZinv } = surchargesTaxes;
      expect(feesSurchargeZinv).toEqual({
        surchargeEnabled: true,
        surchargePercent: 24,
        feePercent: 25,
        surchargeFullFees: true,
        surchargePercentIntl: 26,
        feePercentIntl: 27,
        feeFixed: 28,
        feeFixedIntl: 29,
      });
    });
  });

  describe('feesSurchargeCpoc', () => {
    it('should have correct structure and values', () => {
      const { feesSurchargeCpoc } = surchargesTaxes;
      expect(feesSurchargeCpoc).toEqual({
        surchargeEnabled: true,
        surchargePercent: 30,
        feePercent: 31,
        surchargeFullFees: true,
        feeFixed: 32,
      });
    });
  });

  describe('feesSurchargeVt', () => {
    it('should have correct structure and values', () => {
      const { feesSurchargeVt } = surchargesTaxes;
      expect(feesSurchargeVt).toEqual({
        surchargeEnabled: true,
        surchargePercent: 2,
        feePercent: 2,
        surchargeFullFees: true,
        feeFixed: 32,
      });
    });
  });

  describe('feesSurchargePbl (international rates)', () => {
    it('should have correct structure with international properties', () => {
      const { feesSurchargePbl } = surchargesTaxes;
      expect(feesSurchargePbl).toEqual({
        surchargeEnabled: true,
        surchargePercent: 38,
        feePercent: 39,
        feeFixed: 40,
        surchargeFullFees: true,
        surchargePercentIntl: 41,
        feePercentIntl: 42,
        feeFixedIntl: 43,
      });
    });
  });

  describe('getSurchargesTaxesForRegion function', () => {
    it('should be defined', () => {
      expect(getSurchargesTaxesForRegion).toBeDefined();
      expect(typeof getSurchargesTaxesForRegion).toBe('function');
    });

    it('should return default config for Australia region', () => {
      const australiaConfig = getSurchargesTaxesForRegion(Region.AUSTRALIA);
      expect(australiaConfig.surchargeAllowed).toBe(true);
      expect(australiaConfig.gstPercent).toBe(11);
    });

    it('should return UK overrides for UK region', () => {
      const ukConfig = getSurchargesTaxesForRegion(Region.UK);
      expect(ukConfig.surchargeAllowed).toBe(false); // UK override
      expect(ukConfig.gstPercent).toBe(2000); // UK override
      // Other properties should remain from default
      expect(ukConfig.surchargeEnabled).toBe(false);
      expect(ukConfig.surchargePercent).toBe(1);
    });

    it('should return default config when no region specified', () => {
      const defaultConfig = getSurchargesTaxesForRegion();
      expect(defaultConfig).toBeDefined();
      expect(defaultConfig.surchargeAllowed).toBeDefined();
      expect(defaultConfig.gstPercent).toBeDefined();
    });
  });

  describe('regional configuration differences', () => {
    it('should have different surchargeAllowed values between regions', () => {
      const australiaConfig = getSurchargesTaxesForRegion(Region.AUSTRALIA);
      const ukConfig = getSurchargesTaxesForRegion(Region.UK);
      console.log(ukConfig);

      expect(australiaConfig.surchargeAllowed).toBe(true);
      expect(ukConfig.surchargeAllowed).toBe(false);
    });

    it('should have different gstPercent values between regions', () => {
      const australiaConfig = getSurchargesTaxesForRegion(Region.AUSTRALIA);
      const ukConfig = getSurchargesTaxesForRegion(Region.UK);

      expect(australiaConfig.gstPercent).toBe(11);
      expect(ukConfig.gstPercent).toBe(2000);
    });

    it('should preserve all fee surcharge configurations across regions', () => {
      const australiaConfig = getSurchargesTaxesForRegion(Region.AUSTRALIA);
      const ukConfig = getSurchargesTaxesForRegion(Region.UK);

      // Fee surcharge configurations should be identical
      expect(australiaConfig.feesSurchargeCp).toEqual(ukConfig.feesSurchargeCp);
      expect(australiaConfig.feesSurchargeMoto).toEqual(ukConfig.feesSurchargeMoto);
      expect(australiaConfig.feesSurchargeXinv).toEqual(ukConfig.feesSurchargeXinv);
      expect(australiaConfig.feesSurchargeZinv).toEqual(ukConfig.feesSurchargeZinv);
      expect(australiaConfig.feesSurchargeCpoc).toEqual(ukConfig.feesSurchargeCpoc);
      expect(australiaConfig.feesSurchargeVt).toEqual(ukConfig.feesSurchargeVt);
      expect(australiaConfig.feesSurchargePbl).toEqual(ukConfig.feesSurchargePbl);
    });
  });

  describe('data immutability', () => {
    it('should return different instances for each call', () => {
      const config1 = getSurchargesTaxesForRegion(Region.AUSTRALIA);
      const config2 = getSurchargesTaxesForRegion(Region.AUSTRALIA);

      expect(config1).not.toBe(config2); // Different object references
      expect(config1).toEqual(config2); // Same content
    });

    it('should not affect original when modifying returned config', () => {
      const config = getSurchargesTaxesForRegion(Region.AUSTRALIA);
      const originalSurchargeAllowed = config.surchargeAllowed;

      config.surchargeAllowed = !config.surchargeAllowed;

      const newConfig = getSurchargesTaxesForRegion(Region.AUSTRALIA);
      expect(newConfig.surchargeAllowed).toBe(originalSurchargeAllowed);
    });
  });

  describe('type compliance', () => {
    it('should match SurchargesTaxesSettings interface', () => {
      const config: SurchargesTaxesSettings = getSurchargesTaxesForRegion(Region.AUSTRALIA);

      // Type checking - these should not cause TypeScript errors
      expect(typeof config.surchargeAllowed).toBe('boolean');
      expect(typeof config.surchargeEnabled).toBe('boolean');
      expect(typeof config.surchargePercent).toBe('number');
      expect(typeof config.feePercent).toBe('number');
      expect(typeof config.gstEnabled).toBe('boolean');
      expect(typeof config.gstPercent).toBe('number');
      expect(Array.isArray(config.taxes)).toBe(true);
      expect(typeof config.surchargeFullFees).toBe('boolean');
      expect(typeof config.surchargeEnabledMoto).toBe('boolean');
      expect(typeof config.surchargeFullFeesMoto).toBe('boolean');
      expect(typeof config.surchargePercentMoto).toBe('number');
      expect(typeof config.feePercentMoto).toBe('number');
    });
  });
});
